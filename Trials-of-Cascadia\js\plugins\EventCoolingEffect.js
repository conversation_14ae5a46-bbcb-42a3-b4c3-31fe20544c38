//=============================================================================
// EventCoolingEffect.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc (v1.0) Adds cooling air flow effects to events to simulate fans and ventilation.
 *
 * @param airColor
 * @text Air Flow Color
 * @desc The color of the air flow particles (hex color code).
 * @default #B0E0FF
 * @type string
 *
 * @param airSize
 * @text Air Particle Size
 * @desc The size of individual air particles.
 * @default 2
 * @type number
 * @min 1
 * @max 8
 *
 * @param airCount
 * @text Air Particle Count
 * @desc The number of air particles to generate per burst.
 * @default 6
 * @type number
 * @min 1
 * @max 15
 *
 * @param airSpeed
 * @text Air Flow Speed
 * @desc The speed of air particle movement.
 * @default 1.5
 * @type number
 * @min 0.5
 * @max 4
 * @decimals 1
 *
 * @param airLifetime
 * @text Air Particle Lifetime
 * @desc How long air particles last before disappearing (in frames).
 * @default 90
 * @type number
 * @min 30
 * @max 180
 *
 * @param flowWidth
 * @text Air Flow Width
 * @desc The width of the air flow stream in pixels.
 * @default 48
 * @type number
 * @min 16
 * @max 128
 *
 * @help
 * ============================================================================
 * Event Cooling Effect Plugin
 * ============================================================================
 * 
 * This plugin adds a notetag system to create cooling air flow effects
 * from events, perfect for fans, ventilation systems, and air conditioning.
 * 
 * ============================================================================
 * NOTETAGS
 * ============================================================================
 * 
 * Add this notetag to any event to enable cooling effects:
 * 
 * 1. In the note field at the top of the event page:
 * <cooling>
 * 
 * 2. Within comment event commands in the event's action list:
 * <cooling>
 * 
 * You can also customize the cooling effect for individual events:
 * <cooling:direction=down,width=64,color=#A0D0FF,speed=2>
 * 
 * Available parameters:
 * - direction: down, up, left, right (default: down)
 * - width: Flow width in pixels (16-128)
 * - color: Hex color code (e.g., #A0D0FF for light blue)
 * - speed: Movement speed (0.5-4.0)
 * - count: Particles per burst (1-15)
 * - lifetime: Duration in frames (30-180)
 * 
 * ============================================================================
 * EXAMPLES
 * ============================================================================
 * 
 * Basic downward cooling (in note field or comment):
 * <cooling>
 * 
 * Wide cooling fan:
 * <cooling:width=80,speed=2.5,count=8>
 * 
 * Upward ventilation:
 * <cooling:direction=up,color=#E0F0FF,speed=1.2>
 * 
 * Side air vent:
 * <cooling:direction=right,width=32,color=#C0E0FF>
 * 
 * ============================================================================
 * TERMS OF USE
 * ============================================================================
 * 
 * This plugin is free for both commercial and non-commercial use.
 * Attribution is appreciated but not required.
 * 
 * @license MIT
 */

(() => {
    'use strict';

    // Plugin parameters
    const pluginName = "EventCoolingEffect";
    const parameters = PluginManager.parameters(pluginName);
    
    const AIR_COLOR = parameters['airColor'] || '#B0E0FF';
    const AIR_SIZE = Number(parameters['airSize']) || 2;
    const AIR_COUNT = Number(parameters['airCount']) || 6;
    const AIR_SPEED = Number(parameters['airSpeed']) || 1.5;
    const AIR_LIFETIME = Number(parameters['airLifetime']) || 90;
    const FLOW_WIDTH = Number(parameters['flowWidth']) || 48;

    //=============================================================================
    // Air Particle Class
    //=============================================================================

    class AirParticle extends Sprite {
        constructor(x, y, settings) {
            super();
            this.x = x;
            this.y = y;

            // Air flow physics based on direction
            const direction = settings.direction || 'down';
            const speed = settings.speed * (0.8 + Math.random() * 0.4); // Variable speed
            
            switch (direction) {
                case 'down':
                    this.vx = (Math.random() - 0.5) * 0.5; // Slight horizontal drift
                    this.vy = speed;
                    break;
                case 'up':
                    this.vx = (Math.random() - 0.5) * 0.5;
                    this.vy = -speed;
                    break;
                case 'left':
                    this.vx = -speed;
                    this.vy = (Math.random() - 0.5) * 0.5;
                    break;
                case 'right':
                    this.vx = speed;
                    this.vy = (Math.random() - 0.5) * 0.5;
                    break;
            }

            this.life = settings.lifetime * (0.8 + Math.random() * 0.4); // Variable lifetime
            this.maxLife = this.life;
            this.size = settings.size * (0.7 + Math.random() * 0.6); // Variable size
            this.color = settings.color;
            this.visible = true;
            this._destroyed = false;

            // Air properties
            this.airResistance = 0.995; // Very slight air resistance
            this.turbulence = 0.02; // Small random movements
            this.initialAlpha = 0.4 + Math.random() * 0.3; // Semi-transparent

            this.createAirBitmap();
            this.anchor.set(0.5, 0.5);
            this.blendMode = PIXI.BLEND_MODES.NORMAL; // Normal blending for air
        }

        createAirBitmap() {
            const size = Math.max(4, this.size * 2);
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Convert hex color to RGB
            const hexColor = this.color.replace('#', '');
            const r = parseInt(hexColor.substring(0, 2), 16);
            const g = parseInt(hexColor.substring(2, 4), 16);
            const b = parseInt(hexColor.substring(4, 6), 16);

            // Create soft air particle gradient
            const gradient = ctx.createRadialGradient(center, center, 0, center, center, center);
            gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, ${this.initialAlpha})`); // Center
            gradient.addColorStop(0.6, `rgba(${r}, ${g}, ${b}, ${this.initialAlpha * 0.5})`); // Mid
            gradient.addColorStop(1, 'rgba(255,255,255,0)'); // Transparent edge

            // Draw the air particle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, center, 0, Math.PI * 2);
            ctx.fill();

            this.bitmap = bitmap;
        }

        update() {
            if (this._destroyed) return false;

            // Air particle physics
            this.x += this.vx;
            this.y += this.vy;
            this.life--;

            // Apply slight air resistance
            this.vx *= this.airResistance;
            this.vy *= this.airResistance;

            // Add subtle turbulence
            if (Math.random() < 0.1) {
                this.vx += (Math.random() - 0.5) * this.turbulence;
                this.vy += (Math.random() - 0.5) * this.turbulence;
            }

            // Gradual fade out
            const lifeFactor = this.life / this.maxLife;
            this.opacity = lifeFactor * this.initialAlpha * 255;

            // Slight size variation for air movement effect
            const sizeVariation = 1 + Math.sin(this.life * 0.1) * 0.1;
            this.scale.set(sizeVariation, sizeVariation);

            if (this.life <= 0 || this.opacity < 10) {
                this._destroyed = true;
                return false;
            }

            return true;
        }

        destroy(options) {
            this._destroyed = true;
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
            try {
                super.destroy(options);
            } catch (e) {
                // Ignore destroy errors
            }
        }
    }

    // Utility functions
    function getDefaultSettings() {
        return {
            color: AIR_COLOR,
            size: AIR_SIZE,
            count: AIR_COUNT,
            speed: AIR_SPEED,
            lifetime: AIR_LIFETIME,
            width: FLOW_WIDTH,
            direction: 'down'
        };
    }

    function parseCoolingSettings(eventData) {
        if (!eventData) return null;

        // Check both the note field and comment event commands
        const note = eventData.note || '';
        const commentNotetags = getCoolingCommentNotetags(eventData);

        // Check for cooling tags in either location
        const hasCoolingTag = note.includes('<cooling>') || note.includes('<cooling:') ||
                             commentNotetags.some(comment => comment.includes('<cooling>') || comment.includes('<cooling:'));

        if (!hasCoolingTag) {
            return null;
        }

        const settings = getDefaultSettings();

        // Parse custom parameters from note field first
        let match = note.match(/<cooling:(.*?)>/);
        if (match) {
            parseCoolingParameters(match[1], settings);
        }

        // Parse custom parameters from comment event commands (overrides note field)
        commentNotetags.forEach(comment => {
            const commentMatch = comment.match(/<cooling:(.*?)>/);
            if (commentMatch) {
                parseCoolingParameters(commentMatch[1], settings);
            }
        });

        return settings;
    }

    function getCoolingCommentNotetags(eventData) {
        const comments = [];
        
        // Only check the CURRENT ACTIVE PAGE
        if (eventData.pages) {
            // Find the current active page index
            let activePageIndex = -1;
            
            // Try to get the active page from the game event if it exists
            const gameEvent = $gameMap ? $gameMap.events().find(e => e && e.event() === eventData) : null;
            if (gameEvent && gameEvent._pageIndex !== undefined) {
                activePageIndex = gameEvent._pageIndex;
            } else {
                // Fallback: find the first page that exists
                for (let i = eventData.pages.length - 1; i >= 0; i--) {
                    const page = eventData.pages[i];
                    if (page) {
                        activePageIndex = i;
                        break;
                    }
                }
            }
            
            // Only check the active page
            if (activePageIndex >= 0 && eventData.pages[activePageIndex]) {
                const activePage = eventData.pages[activePageIndex];
                if (activePage.list) {
                    activePage.list.forEach(command => {
                        // Check for comment commands (code 108 for comments, 408 for comment continuation)
                        if (command.code === 108 || command.code === 408) {
                            if (command.parameters && command.parameters[0]) {
                                comments.push(command.parameters[0]);
                            }
                        }
                    });
                }
            }
        }
        
        return comments;
    }

    function parseCoolingParameters(paramString, settings) {
        const params = paramString.split(',');
        params.forEach(param => {
            const [key, value] = param.split('=');
            if (key && value) {
                switch (key.trim()) {
                    case 'color':
                        settings.color = value.trim();
                        break;
                    case 'size':
                        settings.size = Number(value.trim());
                        break;
                    case 'count':
                        settings.count = Number(value.trim());
                        break;
                    case 'speed':
                        settings.speed = Number(value.trim());
                        break;
                    case 'lifetime':
                        settings.lifetime = Number(value.trim());
                        break;
                    case 'width':
                        settings.width = Number(value.trim());
                        break;
                    case 'direction':
                        settings.direction = value.trim().toLowerCase();
                        break;
                }
            }
        });
    }

    //=============================================================================
    // Cooling Effect Manager
    //=============================================================================

    class CoolingEffectManager {
        constructor(scene) {
            this.scene = scene;
            this.airParticles = [];
            this.coolingEvents = new Map();
            this.lastUpdateFrame = 0;
            this.airGenerationTimer = 0;
            this.airGenerationInterval = 15; // Generate air every 15 frames

            this.createAirLayer();
            this.scanForCoolingEvents();
        }

        createAirLayer() {
            this.airLayer = new Sprite();
            this.airLayer.z = 2.9; // Same as sparks - below characters, above tiles

            // Add to tilemap container (same as laser/spark plugins) for proper z-sorting
            if (this.scene._spriteset) {
                const ss = this.scene._spriteset;
                const container = ss._tilemap || ss._effectsContainer || ss;
                container.addChild(this.airLayer);
            } else {
                this.scene.addChild(this.airLayer);
            }
        }

        scanForCoolingEvents() {
            this.coolingEvents.clear();

            if (!$dataMap || !$dataMap.events) return;

            $dataMap.events.forEach((eventData, index) => {
                if (!eventData) return;

                const settings = parseCoolingSettings(eventData);
                if (settings) {
                    this.coolingEvents.set(index, {
                        eventData: eventData,
                        settings: settings,
                        lastGeneration: 0
                    });
                }
            });
        }

        update() {
            if (!this.scene || !this.scene._spriteset) return;

            const currentFrame = Graphics.frameCount;

            // Update existing air particles
            this.updateAirParticles();

            // Generate new air particles
            this.airGenerationTimer++;
            if (this.airGenerationTimer >= this.airGenerationInterval) {
                this.generateAirParticles();
                this.airGenerationTimer = 0;
            }

            this.lastUpdateFrame = currentFrame;
        }

        updateAirParticles() {
            for (let i = this.airParticles.length - 1; i >= 0; i--) {
                const particle = this.airParticles[i];
                if (!particle.update()) {
                    // Remove dead particle
                    this.airLayer.removeChild(particle);
                    particle.destroy();
                    this.airParticles.splice(i, 1);
                }
            }
        }

        generateAirParticles() {
            this.coolingEvents.forEach((coolingData, eventId) => {
                const gameEvent = $gameMap.event(eventId);
                if (!gameEvent || !this.isEventVisible(gameEvent)) return;

                const settings = coolingData.settings;
                const eventX = gameEvent.screenX();
                const eventY = gameEvent.screenY();

                // Generate air particles based on direction and width
                for (let i = 0; i < settings.count; i++) {
                    this.createAirParticle(eventX, eventY, settings);
                }
            });
        }

        createAirParticle(eventX, eventY, settings) {
            // Calculate spawn position based on direction and width
            let spawnX, spawnY;
            const halfWidth = settings.width / 2;

            switch (settings.direction) {
                case 'down':
                    spawnX = eventX + (Math.random() - 0.5) * settings.width;
                    spawnY = eventY - 24; // Start above the event
                    break;
                case 'up':
                    spawnX = eventX + (Math.random() - 0.5) * settings.width;
                    spawnY = eventY + 24; // Start below the event
                    break;
                case 'left':
                    spawnX = eventX + 24; // Start right of the event
                    spawnY = eventY + (Math.random() - 0.5) * settings.width;
                    break;
                case 'right':
                    spawnX = eventX - 24; // Start left of the event
                    spawnY = eventY + (Math.random() - 0.5) * settings.width;
                    break;
                default:
                    spawnX = eventX;
                    spawnY = eventY;
            }

            const particle = new AirParticle(spawnX, spawnY, settings);
            this.airLayer.addChild(particle);
            this.airParticles.push(particle);
        }

        isEventVisible(gameEvent) {
            if (!gameEvent) return false;

            // Check if event is on current map
            if (gameEvent._mapId !== $gameMap.mapId()) return false;

            // Check if event page conditions are met
            if (!gameEvent.findProperPageIndex) return true;

            const pageIndex = gameEvent.findProperPageIndex();
            return pageIndex >= 0;
        }

        refreshCoolingEvents() {
            this.scanForCoolingEvents();
        }

        destroy() {
            // Clean up all air particles
            this.airParticles.forEach(particle => {
                if (particle && particle.destroy) {
                    particle.destroy();
                }
            });
            this.airParticles = [];

            // Remove air layer
            if (this.airLayer && this.airLayer.parent) {
                this.airLayer.parent.removeChild(this.airLayer);
            }

            this.coolingEvents.clear();
        }
    }

    //=============================================================================
    // Scene Integration
    //=============================================================================

    // Scene_Map integration
    const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
    Scene_Map.prototype.createSpriteset = function() {
        _Scene_Map_createSpriteset.call(this);
        this.createCoolingEffectManager();
    };

    Scene_Map.prototype.createCoolingEffectManager = function() {
        if (this._coolingEffectManager) {
            this._coolingEffectManager.destroy();
        }
        this._coolingEffectManager = new CoolingEffectManager(this);
    };

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);
        if (this._coolingEffectManager) {
            this._coolingEffectManager.update();
        }
    };

    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        if (this._coolingEffectManager) {
            this._coolingEffectManager.destroy();
            this._coolingEffectManager = null;
        }
        _Scene_Map_terminate.call(this);
    };

    // Refresh cooling effects when transferring to a new map
    const _Scene_Map_onMapLoaded = Scene_Map.prototype.onMapLoaded;
    Scene_Map.prototype.onMapLoaded = function() {
        _Scene_Map_onMapLoaded.call(this);
        if (this._coolingEffectManager) {
            this._coolingEffectManager.refreshCoolingEvents();
        }
    };

})();
