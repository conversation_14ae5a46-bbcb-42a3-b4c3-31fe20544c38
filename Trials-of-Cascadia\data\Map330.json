{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "PowerPlant", "pan": 0, "pitch": 80, "volume": 50}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 34, "note": "<Zoom: 200%>\n<Fog 1 Settings>\nName: !fogwhite\nOpacity: 100\nvert scroll: +0.5\nColor Tint: 0, 155, 255, 0\nMap Locked\n</Fog 1 Settings>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "Tubing", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 57, "width": 17, "data": [6804, 6828, 6828, 6828, 6828, 6828, 6808, 6800, 6800, 6800, 6804, 6828, 6828, 6828, 6828, 6828, 6808, 6824, 1541, 445, 446, 447, 1541, 6816, 6800, 6800, 6800, 6824, 1541, 445, 446, 447, 1541, 6816, 6824, 1541, 453, 454, 455, 1541, 6840, 6828, 6828, 6828, 6838, 1541, 453, 454, 455, 1541, 6816, 6824, 1541, 461, 462, 463, 1541, 1541, 1541, 1541, 1541, 1541, 1541, 461, 462, 463, 1541, 6816, 6825, 5123, 5122, 5122, 5122, 5126, 1541, 1541, 1541, 1541, 1541, 5123, 5122, 5122, 5122, 5126, 6816, 6832, 5129, 5128, 5128, 5128, 5132, 1541, 1541, 1541, 1541, 1541, 5129, 5128, 5128, 5128, 5132, 6816, 6832, 4050, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4052, 6816, 6832, 4032, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4040, 6816, 6832, 4056, 6846, 4044, 4044, 4024, 4016, 4016, 4016, 4016, 4016, 4020, 4044, 4044, 6846, 4054, 6816, 6832, 5135, 1541, 5135, 5126, 4056, 4024, 4016, 4016, 4016, 4020, 4054, 5123, 5135, 1541, 5135, 6816, 6832, 4290, 1541, 4292, 5125, 5127, 4056, 4044, 4044, 4044, 4054, 5127, 5125, 4290, 1541, 4292, 6816, 6832, 4288, 1541, 4288, 5121, 5125, 5131, 5134, 5122, 5131, 5134, 5125, 5124, 4288, 1541, 4288, 6816, 6832, 4297, 4289, 4295, 5121, 5124, 4290, 4292, 5125, 4290, 4292, 5121, 5124, 4297, 4289, 4295, 6816, 6832, 5123, 5122, 5126, 5121, 5124, 4296, 4294, 5125, 4296, 4294, 5121, 5124, 5123, 5122, 5126, 6816, 6832, 5129, 5128, 5132, 5129, 5128, 5131, 5134, 5120, 5131, 5134, 5128, 5132, 5129, 5128, 5132, 6816, 6819, 6833, 6833, 6833, 6833, 6845, 4290, 4292, 5125, 4290, 4292, 6843, 6833, 6833, 6833, 6833, 6809, 6832, 1541, 445, 446, 447, 1541, 4296, 4294, 5125, 4296, 4294, 1541, 445, 446, 447, 1541, 6816, 6824, 1541, 453, 454, 455, 1541, 5131, 5134, 5128, 5131, 5134, 1541, 453, 454, 455, 1541, 6816, 6824, 1541, 461, 462, 463, 1541, 4299, 4289, 4289, 4289, 4301, 1541, 461, 462, 463, 1541, 6816, 6824, 5123, 5122, 5127, 5122, 5123, 5131, 5134, 5122, 5131, 5134, 5126, 5122, 5127, 5122, 5126, 6816, 6824, 5125, 6846, 5125, 5120, 5125, 4290, 4292, 5125, 4290, 4292, 5125, 5120, 5125, 6846, 5125, 6816, 6824, 5133, 1541, 5133, 5120, 5125, 4296, 4294, 5125, 4296, 4294, 5125, 5120, 5133, 1541, 5133, 6816, 6824, 4290, 1541, 4292, 5120, 5129, 5131, 5134, 5120, 5131, 5134, 5132, 5120, 4290, 1541, 4292, 6816, 6824, 4288, 1541, 4288, 5124, 6846, 4290, 4292, 5125, 4290, 4292, 6846, 5121, 4288, 1541, 4288, 6816, 6824, 4297, 4289, 4295, 5124, 1541, 4296, 4294, 5125, 4296, 4294, 1541, 5121, 4297, 4289, 4295, 6816, 6824, 5125, 5133, 5125, 5124, 1541, 5131, 5134, 5128, 5131, 5134, 1541, 5121, 5120, 5120, 5124, 6816, 6824, 5125, 6846, 5125, 5124, 1541, 4299, 4289, 4289, 4289, 4301, 1541, 5121, 5125, 6846, 5125, 6816, 6824, 5133, 1541, 5133, 5124, 4298, 4194, 4180, 4181, 4193, 4197, 4298, 5121, 5133, 1541, 5133, 6816, 6824, 4290, 1541, 4292, 5125, 6846, 4176, 4160, 4184, 7199, 4192, 6846, 5121, 4290, 1541, 4292, 6816, 6824, 4288, 1541, 4288, 5125, 1541, 4176, 4160, 4162, 4180, 4186, 1541, 5121, 4288, 1541, 4288, 6816, 6824, 4297, 4289, 4295, 5125, 1541, 4200, 4188, 4188, 4188, 4198, 1541, 5121, 4297, 4289, 4295, 6816, 6824, 5123, 5122, 5126, 5124, 1541, 4299, 4289, 4289, 4289, 4301, 1541, 5121, 5120, 5124, 5124, 6816, 6824, 5129, 5128, 5132, 5132, 5131, 5130, 5130, 5130, 5130, 5130, 5134, 5129, 5129, 5132, 5132, 6816, 6802, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6820, 6801, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3076, 3077, 3089, 3093, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3056, 3080, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3056, 3058, 3076, 3082, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3084, 3084, 3084, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 449, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 426, 0, 0, 0, 0, 0, 0, 0, 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 293, 0, 0, 0, 0, 0, 293, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 441, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4194, 4180, 4180, 4180, 4196, 50, 51, 0, 50, 51, 4194, 4180, 4180, 4180, 4196, 0, 0, 4200, 4188, 4188, 4188, 4198, 34, 35, 0, 34, 35, 4200, 4188, 4188, 4188, 4198, 0, 0, 209, 209, 209, 209, 209, 42, 43, 0, 42, 43, 209, 209, 209, 209, 209, 0, 0, 0, 0, 0, 434, 34, 35, 0, 0, 0, 34, 35, 451, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 43, 0, 0, 0, 42, 43, 0, 0, 0, 0, 0, 0, 0, 460, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 460, 0, 0, 0, 0, 468, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 468, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 460, 0, 293, 0, 0, 0, 0, 0, 293, 0, 460, 0, 293, 0, 0, 0, 0, 468, 0, 0, 0, 0, 0, 0, 0, 0, 0, 468, 0, 0, 0, 0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 460, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 460, 0, 0, 0, 0, 468, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 468, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 460, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 460, 0, 0, 0, 0, 468, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 468, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "<bubbles><hitbox up: 1><hitbox left: 1><hitbox right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$PowerCore", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Glow Rate: 50%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 5}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToCustomColor", "OVERLAY: Change to Custom Color", {"Color:str": "#000000", "Opacity:num": "150", "Duration:num": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Color = #000000"]}, {"code": 657, "indent": 0, "parameters": ["Opacity = 150"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, {"id": 3, "name": "EV003", "note": "<bubbles><hitbox up: 1><hitbox left: 1><hitbox right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$PowerCore", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Glow Rate: 50%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 5}, {"id": 4, "name": "EV004", "note": "<bubbles><hitbox up: 1><hitbox left: 1><hitbox right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$PowerCore", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Glow Rate: 50%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 5}, {"id": 5, "name": "EV005", "note": "<bubbles><hitbox up: 1><hitbox left: 1><hitbox right: 1>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$PowerCore", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Glow Rate: 50%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 5}, {"id": 6, "name": "laser", "note": "<Sprite Offset X: -24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<hitbox right: 4>"]}, {"code": 408, "indent": 0, "parameters": ["<laser: direction=right, length=215, color=#00FFFF, width=3>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 17}, {"id": 7, "name": "laser", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 108, "indent": 0, "parameters": ["<spark>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 18}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: White>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 4}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: White>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 4}, {"id": 10, "name": "EV010", "note": "<Sprite Offset Y: +48>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!TV_screens_remaster", "direction": 4, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 200>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 65%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 7}, {"id": 11, "name": "EV011", "note": "<Sprite Offset Y: +48>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!TV_screens_remaster", "direction": 6, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 200>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 65%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 5}, {"id": 12, "name": "EV012", "note": "<Sprite Offset Y: +48>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!TV_screens_remaster", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 200>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 65%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 5}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: White>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 65%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 3}, {"id": 14, "name": "EV014", "note": "<Sprite Offset Y: +48>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!TV_screens_remaster", "direction": 8, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 200>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 65%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 7}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 21}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 21}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 27}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 27}, null, null, null, null, null, null, null, null, null, null, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 16}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 16}, {"id": 31, "name": "laser", "note": "<Sprite Offset X: -24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<hitbox right: 4>"]}, {"code": 408, "indent": 0, "parameters": ["<laser: direction=right, length=215, color=#00FFFF, width=3>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Thunder3", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[0, 255, 255, 170], 30, false]}, {"code": 225, "indent": 0, "parameters": [2, 2, 30, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 18}, {"id": 32, "name": "laser", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 108, "indent": 0, "parameters": ["<spark>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 17}, {"id": 33, "name": "EV033", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!lights_remaster", "direction": 2, "pattern": 0, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 28}, null, {"id": 35, "name": "EV035", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 272, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 50>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "SelfSwitchABCD", "Self Switch: A B C D", {"MapId:eval": "0", "EventId:eval": "6", "Letter:str": "A", "Break": "", "Value:str": "Toggle"}]}, {"code": 657, "indent": 0, "parameters": ["Map ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 6"]}, {"code": 657, "indent": 0, "parameters": ["Letter = A"]}, {"code": 657, "indent": 0, "parameters": ["- = "]}, {"code": 657, "indent": 0, "parameters": ["Value = Toggle"]}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "SelfSwitchABCD", "Self Switch: A B C D", {"MapId:eval": "0", "EventId:eval": "7", "Letter:str": "A", "Break": "", "Value:str": "Toggle"}]}, {"code": 657, "indent": 0, "parameters": ["Map ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 7"]}, {"code": 657, "indent": 0, "parameters": ["Letter = A"]}, {"code": 657, "indent": 0, "parameters": ["- = "]}, {"code": 657, "indent": 0, "parameters": ["Value = Toggle"]}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "SelfSwitchABCD", "Self Switch: A B C D", {"MapId:eval": "0", "EventId:eval": "31", "Letter:str": "A", "Break": "", "Value:str": "Toggle"}]}, {"code": 657, "indent": 0, "parameters": ["Map ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 31"]}, {"code": 657, "indent": 0, "parameters": ["Letter = A"]}, {"code": 657, "indent": 0, "parameters": ["- = "]}, {"code": 657, "indent": 0, "parameters": ["Value = Toggle"]}, {"code": 357, "indent": 0, "parameters": ["Wave 1/VisuMZ_1_EventsMoveCore", "SelfSwitchABCD", "Self Switch: A B C D", {"MapId:eval": "0", "EventId:eval": "32", "Letter:str": "A", "Break": "", "Value:str": "Toggle"}]}, {"code": 657, "indent": 0, "parameters": ["Map ID = 0"]}, {"code": 657, "indent": 0, "parameters": ["Event ID = 32"]}, {"code": 657, "indent": 0, "parameters": ["Letter = A"]}, {"code": 657, "indent": 0, "parameters": ["- = "]}, {"code": 657, "indent": 0, "parameters": ["Value = Toggle"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 18}, null, null, {"id": 38, "name": "EV038", "note": "<shadow>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 8}, {"id": 39, "name": "EV039", "note": "<shadow>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 8}, {"id": 40, "name": "EV040", "note": "<shadow>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 8}, {"id": 41, "name": "EV041", "note": "<shadow>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "android", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 8}, null, null, {"id": 44, "name": "EV044", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 9}, {"id": 45, "name": "EV045", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 100>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 5%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 95%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Blink Rate: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Flare Rate: 1%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 9}, null, null, null, null, null, null, {"id": 52, "name": "EV052", "note": "<Sprite Offset Y: +24>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Cyberpunk_decoration", "direction": 2, "pattern": 0, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 6}]}