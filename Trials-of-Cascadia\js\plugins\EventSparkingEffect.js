//=============================================================================
// EventSparkingEffect.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc (v1.0) Adds sparking effects to events to simulate damaged machines.
 *
 * @param sparkColor
 * @text Spark Color
 * @desc The color of the sparks (hex color code).
 * @default #FFFF00
 * @type string
 *
 * @param sparkSize
 * @text Spark Size
 * @desc The size of individual sparks.
 * @default 3
 * @type number
 * @min 1
 * @max 10
 *
 * @param sparkCount
 * @text Spark Count
 * @desc The number of sparks to generate.
 * @default 8
 * @type number
 * @min 1
 * @max 20
 *
 * @param sparkSpeed
 * @text Spark Speed
 * @desc The speed of spark movement.
 * @default 2
 * @type number
 * @min 0.5
 * @max 5
 * @decimals 1
 *
 * @param sparkLifetime
 * @text Spark Lifetime
 * @desc How long sparks last before disappearing (in frames).
 * @default 60
 * @type number
 * @min 20
 * @max 120
 *
 * @param sparkFlicker
 * @text Spark Flicker
 * @desc Whether sparks should flicker on and off.
 * @default true
 * @type boolean
 *
 * @help
 * ============================================================================
 * Event Sparking Effect Plugin
 * ============================================================================
 * 
 * This plugin adds a notetag system to make events appear to be sparking
 * like damaged machines. Perfect for creating atmosphere in sci-fi or
 * industrial settings.
 * 
 * ============================================================================
 * NOTETAGS
 * ============================================================================
 * 
 * Add this notetag to any event to enable sparking. You can place it in either:
 * 
 * 1. The note field at the top of the event page:
 * <spark>
 * 
 * 2. Within comment event commands in the event's action list:
 * <spark>
 * 
 * You can also customize the sparking for individual events:
 * <spark:color=#FF0000,size=5,count=12,speed=3>
 * 
 * Note: If you have the same notetag in both locations, the comment event command
 * settings will override the note field settings.
 * 
 * DYNAMIC PAGE SWITCHING:
 * The plugin automatically detects when you switch between event pages and will
 * start/stop sparking effects accordingly. If you switch to a page without the
 * <spark> notetag, the effect will automatically stop.
 * 
 * Available parameters:
 * - color: Hex color code (e.g., #FF0000 for red)
 * - size: Spark size (1-10)
 * - count: Number of sparks (1-20)
 * - speed: Movement speed (0.5-5.0)
 * - lifetime: Duration in frames (20-120)
 * - flicker: true/false
 * 
 * ============================================================================
 * EXAMPLES
 * ============================================================================
 * 
 * Basic sparking (in note field or comment event command):
 * <spark>
 * 
 * Red sparks with custom settings:
 * <spark:color=#FF0000,size=4,count=10,speed=2.5>
 * 
 * Blue flickering sparks:
 * <spark:color=#0088FF,size=2,count=15,flicker=true>
 * 
 * Using in comment event command (overrides note field):
 * Comment: <spark:color=#00FF00,size=3,count=8>
 * 
 * ============================================================================
 * TERMS OF USE
 * ============================================================================
 * 
 * This plugin is free for both commercial and non-commercial use.
 * Attribution is appreciated but not required.
 * 
 * @license MIT
 */

(() => {
    'use strict';

    // Plugin parameters
    const pluginName = "EventSparkingEffect";
    const parameters = PluginManager.parameters(pluginName);
    
    const SPARK_COLOR = parameters['sparkColor'] || '#4080FF'; // Blue sparks
    const SPARK_SIZE = Number(parameters['sparkSize']) || 1.5; // Much smaller
    const SPARK_COUNT = Number(parameters['sparkCount']) || 4; // Fewer sparks
    const SPARK_SPEED = Number(parameters['sparkSpeed']) || 3; // Faster initial speed
    const SPARK_LIFETIME = Number(parameters['sparkLifetime']) || 30; // Shorter lifetime
    const SPARK_FLICKER = parameters['sparkFlicker'] === 'true';

    //=============================================================================
    // Object Pooling System
    //=============================================================================

    class ObjectPool {
        constructor(createFn, resetFn, maxSize = 50) {
            this.createFn = createFn;
            this.resetFn = resetFn;
            this.maxSize = maxSize;
            this.pool = [];
            this.active = [];
        }

        get() {
            let obj;
            if (this.pool.length > 0) {
                obj = this.pool.pop();
            } else {
                obj = this.createFn();
            }
            this.active.push(obj);
            return obj;
        }

        release(obj) {
            const index = this.active.indexOf(obj);
            if (index !== -1) {
                this.active.splice(index, 1);
                if (this.pool.length < this.maxSize) {
                    this.resetFn(obj);
                    this.pool.push(obj);
                } else {
                    obj.destroy();
                }
            }
        }

        clear() {
            this.active.forEach(obj => obj.destroy());
            this.pool.forEach(obj => obj.destroy());
            this.active = [];
            this.pool = [];
        }
    }

    //=============================================================================
    // Localized Flash Effect
    //=============================================================================

    class LocalizedFlash {
        static create(scene, x, y, intensity = 0.4, duration = 8, color = '#4080FF', radius = 64) {
            if (!scene._sparkFlashLayer) {
                scene._sparkFlashLayer = new Sprite();
                scene._sparkFlashLayer.z = 4; // Same level as sparks

                // Add to spriteset for zoom compatibility
                if (scene._spriteset) {
                    scene._spriteset.addChild(scene._sparkFlashLayer);
                } else {
                    scene.addChild(scene._sparkFlashLayer);
                }
            }

            const flash = new Sprite();
            const size = radius * 2;
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Convert hex to RGB
            const hexColor = color.replace('#', '');
            const r = parseInt(hexColor.substring(0, 2), 16);
            const g = parseInt(hexColor.substring(2, 4), 16);
            const b = parseInt(hexColor.substring(4, 6), 16);

            // Create radial gradient for localized flash
            const gradient = ctx.createRadialGradient(center, center, 0, center, center, radius);
            gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, ${intensity})`); // Bright center
            gradient.addColorStop(0.3, `rgba(${r}, ${g}, ${b}, ${intensity * 0.7})`); // Medium
            gradient.addColorStop(0.6, `rgba(${r}, ${g}, ${b}, ${intensity * 0.3})`); // Fade
            gradient.addColorStop(1, 'rgba(255,255,255,0)'); // Transparent edge

            // Draw the flash
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, radius, 0, Math.PI * 2);
            ctx.fill();

            flash.bitmap = bitmap;
            flash.x = x;
            flash.y = y;
            flash.anchor.set(0.5, 0.5);
            flash.blendMode = PIXI.BLEND_MODES.ADD;
            flash.opacity = 255;
            flash._flashDuration = duration;
            flash._maxDuration = duration;
            flash._destroyed = false;

            scene._sparkFlashLayer.addChild(flash);

            // Auto-remove after duration with pulsing effect
            flash.update = function() {
                // Safety check - ensure the sprite is still valid
                if (!this || this._destroyed || !this.scale || !this.parent) {
                    this._destroyed = true;
                    return false;
                }
                
                this._flashDuration--;
                const alpha = this._flashDuration / this._maxDuration;

                // Add slight pulsing effect
                const pulse = 1 + Math.sin(this._flashDuration * 0.5) * 0.1;
                this.opacity = alpha * 255;
                
                // Safe scale access with additional null checks
                try {
                    if (this.scale && typeof this.scale.set === 'function') {
                        this.scale.set(pulse, pulse);
                    }
                } catch (e) {
                    console.warn('EventSparkingEffect: Flash scale error:', e);
                    this._destroyed = true;
                    return false;
                }

                if (this._flashDuration <= 0) {
                    this._destroyed = true;
                    try {
                        if (scene._sparkFlashLayer && this.parent) {
                            scene._sparkFlashLayer.removeChild(this);
                        }
                        if (this.bitmap) {
                            this.bitmap.destroy();
                        }
                        this.destroy();
                    } catch (e) {
                        console.warn('EventSparkingEffect: Flash cleanup error:', e);
                    }
                    return false;
                }
                return true;
            };

            return flash;
        }
    }

    //=============================================================================
    // Electrical Smoke Puff Class
    //=============================================================================

    class ElectricalSmoke extends Sprite {
        constructor(x, y, settings) {
            super();
            this.x = x;
            this.y = y;

            // Smoke physics - slow upward drift
            this.vx = (Math.random() - 0.5) * 0.5; // Very slow horizontal drift
            this.vy = -0.3 - Math.random() * 0.2; // Upward drift

            this.life = 120 + Math.random() * 60; // Long-lived (2-3 seconds)
            this.maxLife = this.life;
            this.size = 4 + Math.random() * 8; // Small puffs
            this.color = settings.color;
            this.visible = true;
            this._destroyed = false;

            // Smoke properties
            this.expansion = 1.01; // Slowly expands
            this.fadeRate = 0.98; // Slow fade
            this.initialAlpha = 0.3 + Math.random() * 0.2; // Semi-transparent

            this.createSmokeBitmap();
            this.anchor.set(0.5, 0.5);
            this.blendMode = PIXI.BLEND_MODES.NORMAL; // Normal blending for smoke
        }

        createSmokeBitmap() {
            const size = Math.max(16, this.size * 2);
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Create wispy smoke gradient (gray/dark blue)
            const gradient = ctx.createRadialGradient(center, center, 0, center, center, center);
            gradient.addColorStop(0, `rgba(80, 80, 120, ${this.initialAlpha})`); // Dark blue-gray center
            gradient.addColorStop(0.4, `rgba(60, 60, 100, ${this.initialAlpha * 0.7})`); // Medium
            gradient.addColorStop(0.7, `rgba(40, 40, 80, ${this.initialAlpha * 0.4})`); // Fade
            gradient.addColorStop(1, 'rgba(20,20,40,0)'); // Transparent edge

            // Draw the smoke puff
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, center, 0, Math.PI * 2);
            ctx.fill();

            this.bitmap = bitmap;
        }

        update() {
            if (this._destroyed) return false;

            // Smoke physics - slow drift upward
            this.x += this.vx;
            this.y += this.vy;
            this.life--;

            // Slow expansion and fade
            this.scale.x *= this.expansion;
            this.scale.y *= this.expansion;

            // Gradual fade out
            const lifeFactor = this.life / this.maxLife;
            this.opacity = lifeFactor * this.initialAlpha * 255;

            // Slight random drift changes
            if (Math.random() < 0.05) {
                this.vx += (Math.random() - 0.5) * 0.1;
                this.vy += (Math.random() - 0.5) * 0.05;
            }

            if (this.life <= 0 || this.opacity < 5) {
                this._destroyed = true;
                return false;
            }

            return true;
        }

        destroy(options) {
            this._destroyed = true;
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
            try {
                super.destroy(options);
            } catch (e) {
                // Ignore destroy errors
            }
        }
    }

    //=============================================================================
    // Electrical Arc Class
    //=============================================================================

    class ElectricalArc extends Sprite {
        constructor(x, y, settings) {
            super();
            this.x = x;
            this.y = y;
            this.life = 8 + Math.random() * 12; // Very short life (8-20 frames)
            this.maxLife = this.life;
            this.color = settings.color;
            this.visible = true;
            this._destroyed = false;

            // Arc properties
            this.length = 8 + Math.random() * 16; // 8-24 pixel length
            this.angle = Math.random() * Math.PI * 2; // Random direction
            this.thickness = 0.5 + Math.random() * 1; // Very thin lines
            this.segments = 3 + Math.floor(Math.random() * 4); // 3-6 segments for jagged look

            this.createArcBitmap();
            this.anchor.set(0.5, 0.5);
            this.blendMode = PIXI.BLEND_MODES.ADD;
        }

        createArcBitmap() {
            const size = Math.max(32, this.length * 2); // Canvas big enough for arc
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Convert hex color to RGB
            const hexColor = this.color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Draw jagged electrical arc
            ctx.strokeStyle = `rgba(255, 255, 255, 0.9)`; // Bright white core
            ctx.lineWidth = this.thickness;
            ctx.lineCap = 'round';

            // Create jagged path
            const startX = center - Math.cos(this.angle) * this.length / 2;
            const startY = center - Math.sin(this.angle) * this.length / 2;
            const endX = center + Math.cos(this.angle) * this.length / 2;
            const endY = center + Math.sin(this.angle) * this.length / 2;

            ctx.beginPath();
            ctx.moveTo(startX, startY);

            // Create jagged segments
            for (let i = 1; i < this.segments; i++) {
                const t = i / this.segments;
                const baseX = startX + (endX - startX) * t;
                const baseY = startY + (endY - startY) * t;

                // Add random jaggedness perpendicular to the arc
                const perpAngle = this.angle + Math.PI / 2;
                const jag = (Math.random() - 0.5) * 6; // Random offset
                const jagX = baseX + Math.cos(perpAngle) * jag;
                const jagY = baseY + Math.sin(perpAngle) * jag;

                ctx.lineTo(jagX, jagY);
            }

            ctx.lineTo(endX, endY);
            ctx.stroke();

            // Add colored glow
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.6)`;
            ctx.lineWidth = this.thickness + 1;
            ctx.stroke();

            this.bitmap = bitmap;
        }

        update() {
            if (this._destroyed) return false;

            this.life--;

            // Very rapid flickering for electrical effect
            this.visible = Math.random() > 0.4; // Flickers 60% of the time

            // Fade out quickly
            const alpha = this.life / this.maxLife;
            this.opacity = alpha * 255;

            if (this.life <= 0) {
                this._destroyed = true;
                return false;
            }

            return true;
        }

        destroy(options) {
            this._destroyed = true;
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
            super.destroy(options);
        }
    }

    //=============================================================================
    // Spark Class (Using Bitmap/Canvas approach like FF6 Airship)
    //=============================================================================

    class Spark extends Sprite {
        constructor(x, y, settings) {
            super();
            this.x = x;
            this.y = y;

            // More natural spark physics
            const angle = Math.random() * Math.PI * 2; // Random direction
            const speed = settings.speed * (0.5 + Math.random() * 0.5); // Variable speed
            this.vx = Math.cos(angle) * speed;
            this.vy = Math.sin(angle) * speed - 1; // Slight upward bias

            this.life = settings.lifetime * (0.7 + Math.random() * 0.6); // Variable lifetime
            this.maxLife = this.life;
            this.size = settings.size * (0.5 + Math.random() * 0.5); // Variable size
            this.color = settings.color;
            this.flicker = settings.flicker;
            this.visible = true;
            this._destroyed = false;

            // Physics properties for natural behavior
            this.gravity = 0.15; // Gravity effect
            this.airResistance = 0.96; // Air resistance
            this.sparkIntensity = 1.0; // Initial brightness

            // Trail system for particle trails
            this.trail = [];
            this.maxTrailLength = 4; // Short trails
            this.trailFadeRate = 0.7; // How quickly trail fades

            // Create bitmap for the spark using canvas
            this.createSparkBitmap();
            this.anchor.set(0.5, 0.5);
            this.blendMode = PIXI.BLEND_MODES.ADD; // Additive blending for glow effect
        }

        createSparkBitmap() {
            // Much smaller spark - more realistic
            const size = Math.max(4, this.size * 2); // Smaller canvas
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Convert hex color to RGB for gradient
            const hexColor = this.color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Create tighter radial gradient for smaller, more intense spark
            const coreRadius = Math.max(0.5, this.size * 0.3);
            const glowRadius = Math.max(1, this.size * 0.8);

            const gradient = ctx.createRadialGradient(center, center, 0, center, center, glowRadius);
            gradient.addColorStop(0, `rgba(255, 255, 255, 1.0)`); // Bright white core
            gradient.addColorStop(0.2, `rgba(${r}, ${g}, ${b}, 0.9)`); // Color transition
            gradient.addColorStop(0.5, `rgba(${r}, ${g}, ${b}, 0.6)`); // Main color
            gradient.addColorStop(0.8, `rgba(${r}, ${g}, ${b}, 0.2)`); // Fade
            gradient.addColorStop(1, 'rgba(255,255,255,0)'); // Transparent edge

            // Draw the spark
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, glowRadius, 0, Math.PI * 2);
            ctx.fill();

            // Add very bright pinpoint core
            ctx.fillStyle = 'rgba(255,255,255,1.0)';
            ctx.beginPath();
            ctx.arc(center, center, coreRadius, 0, Math.PI * 2);
            ctx.fill();

            this.bitmap = bitmap;
        }

        update() {
            if (this._destroyed) return false;

            // Store previous position for trail
            this.trail.unshift({ x: this.x, y: this.y, alpha: this.sparkIntensity });

            // Limit trail length
            if (this.trail.length > this.maxTrailLength) {
                this.trail.pop();
            }

            // Natural spark physics
            this.x += this.vx;
            this.y += this.vy;
            this.life--;

            // Apply gravity and air resistance for realistic movement
            this.vy += this.gravity; // Gravity pulls down
            this.vx *= this.airResistance; // Air resistance slows horizontal movement
            this.vy *= this.airResistance; // Air resistance slows vertical movement

            // Spark intensity fades over time
            this.sparkIntensity = this.life / this.maxLife;

            // Update opacity with more dramatic fade
            const lifeFactor = this.life / this.maxLife;
            const alpha = Math.pow(lifeFactor, 0.5); // Square root for more natural fade
            this.opacity = Math.max(0.05, alpha * 255);

            // More aggressive flickering for electrical effect
            if (this.flicker) {
                this.visible = Math.random() > 0.3; // More frequent flickering
            }

            // Random intensity variations for electrical crackling effect
            if (Math.random() < 0.1) {
                this.opacity *= (0.3 + Math.random() * 0.7); // Random brightness variations
            }

            // Update trail alpha values
            for (let i = 0; i < this.trail.length; i++) {
                if (this.trail[i]) {
                    this.trail[i].alpha *= this.trailFadeRate;
                }
            }

            // Update trail sprites (only every few frames for performance)
            if (this.life % 3 === 0) {
                this.updateTrailSprites();
            }

            if (this.life <= 0) {
                this._destroyed = true;
                return false;
            }

            return true;
        }

        // Simplified trail rendering using separate sprites
        updateTrailSprites() {
            // Clean up old trail sprites
            if (this.trailSprites) {
                this.trailSprites.forEach(sprite => {
                    try {
                        if (sprite && sprite.parent) sprite.parent.removeChild(sprite);
                        if (sprite) sprite.destroy();
                    } catch (e) {
                        // Ignore cleanup errors
                    }
                });
            }
            this.trailSprites = [];

            // Create new trail sprites
            if (this.trail.length > 1 && this.parent) {
                for (let i = 0; i < this.trail.length - 1; i++) {
                    const current = this.trail[i];
                    if (!current) continue;

                    const trailAlpha = current.alpha * 0.3;

                    if (trailAlpha > 0.01) {
                        try {
                            const trailSprite = new Sprite();
                            const bitmap = new Bitmap(2, 2);

                            // Convert hex color to RGB
                            const hexColor = this.color.replace('#', '');
                            const r = parseInt(hexColor.substring(0, 2), 16);
                            const g = parseInt(hexColor.substring(2, 4), 16);
                            const b = parseInt(hexColor.substring(4, 6), 16);

                            bitmap.fillAll(`rgba(${r}, ${g}, ${b}, ${trailAlpha})`);
                            trailSprite.bitmap = bitmap;
                            trailSprite.x = current.x;
                            trailSprite.y = current.y;
                            trailSprite.anchor.set(0.5, 0.5);
                            trailSprite.blendMode = PIXI.BLEND_MODES.ADD;

                            this.parent.addChild(trailSprite);
                            this.trailSprites.push(trailSprite);
                        } catch (e) {
                            // Skip this trail sprite if there's an error
                            console.warn('EventSparkingEffect: Trail sprite creation failed:', e);
                        }
                    }
                }
            }
        }

        destroy(options) {
            this._destroyed = true;

            // Clean up trail sprites safely
            if (this.trailSprites) {
                this.trailSprites.forEach(sprite => {
                    try {
                        if (sprite && sprite.parent) sprite.parent.removeChild(sprite);
                        if (sprite) sprite.destroy();
                    } catch (e) {
                        // Ignore cleanup errors
                    }
                });
                this.trailSprites = [];
            }

            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }

            try {
                super.destroy(options);
            } catch (e) {
                // Ignore destroy errors
            }
        }
    }

    //=============================================================================
    // Event Spark Manager (attached to Scene_Map)
    //=============================================================================

    class EventSparkManager {
        constructor(scene) {
            this.scene = scene;
            this.sparkingEvents = new Map(); // eventId -> spark data
            this.sparkTimer = 0;
            this.arcTimer = 0;
            this.smokeTimer = 0;
            this.sparkInterval = 15; // Base interval for sparks
            this.arcInterval = 8; // Base interval for arcs
            this.smokeInterval = 120; // Base interval for smoke (2 seconds)
            this.nextSparkDelay = this.getRandomizedInterval(this.sparkInterval);
            this.nextArcDelay = this.getRandomizedInterval(this.arcInterval);
            this.nextSmokeDelay = this.getRandomizedInterval(this.smokeInterval);

            // Object pools for performance
            this.sparkPool = new ObjectPool(
                () => new Spark(0, 0, getDefaultSettings()),
                (spark) => this.resetSpark(spark),
                30
            );
            this.arcPool = new ObjectPool(
                () => new ElectricalArc(0, 0, getDefaultSettings()),
                (arc) => this.resetArc(arc),
                20
            );
            this.smokePool = new ObjectPool(
                () => new ElectricalSmoke(0, 0, getDefaultSettings()),
                (smoke) => this.resetSmoke(smoke),
                10
            );

            // Active sprites (for updates)
            this.activeSparkSprites = [];
            this.activeArcSprites = [];
            this.activeSmokeSprites = [];

            // Screen flash tracking
            this.activeFlashes = [];

            // Create spark layer as child of spriteset for automatic zoom compatibility
            this.sparkLayer = new Sprite();
            this.sparkLayer.z = 4; // Above characters but below UI

            // Add to spriteset instead of scene for automatic zoom scaling
            if (scene._spriteset) {
                scene._spriteset.addChild(this.sparkLayer);
            } else {
                scene.addChild(this.sparkLayer);
            }

            console.log('EventSparkingEffect: EventSparkManager created with object pooling');
        }

        addSparkingEvent(eventId, settings) {
            this.sparkingEvents.set(eventId, {
                settings: settings,
                lastSparkTime: 0
            });
            console.log(`EventSparkingEffect: Added sparking event ${eventId}:`, settings);
        }

        removeSparkingEvent(eventId) {
            this.sparkingEvents.delete(eventId);
            console.log(`EventSparkingEffect: Removed sparking event ${eventId} from manager`);
            
            // Clean up any active effects for this event
            this.cleanupEventEffects(eventId);
        }

        cleanupEventEffects(eventId) {
            // This method can be expanded later to clean up specific effects
            // For now, we just log the cleanup
            console.log(`EventSparkingEffect: Cleaned up effects for event ${eventId}`);
            
            // Clean up any active flashes that might be related to this event
            // This helps prevent the scale error when events are removed
            this.activeFlashes = this.activeFlashes.filter(flash => {
                if (!flash || flash._destroyed) {
                    return false;
                }
                return true;
            });
        }

        refreshAllSparkingEvents() {
            // Clear all current sparking events
            this.sparkingEvents.clear();
            
            // Clean up any existing flashes to prevent errors
            this.cleanupAllFlashes();
            
            // Re-scan all events for spark tags
            if ($dataMap && $dataMap.events) {
                $dataMap.events.forEach((eventData, index) => {
                    if (eventData && parseSparkSettings(eventData)) {
                        const settings = parseSparkSettings(eventData);
                        if (settings) {
                            this.addSparkingEvent(index, settings);
                        }
                    }
                });
            }
            
            console.log('EventSparkingEffect: Refreshed all sparking events');
        }

        cleanupAllFlashes() {
            // Safely clean up all active flashes
            this.activeFlashes.forEach(flash => {
                try {
                    if (flash && !flash._destroyed) {
                        flash._destroyed = true;
                        if (flash.parent) {
                            flash.parent.removeChild(flash);
                        }
                        if (flash.bitmap) {
                            flash.bitmap.destroy();
                        }
                        flash.destroy();
                    }
                } catch (e) {
                    console.warn('EventSparkingEffect: Flash cleanup error:', e);
                }
            });
            this.activeFlashes = [];
        }

        resetSpark(spark) {
            if (!spark) return;

            spark.visible = true;
            spark._destroyed = false;
            spark.trail = [];
            spark.life = 0;
            spark.maxLife = 0;
            spark.opacity = 255;

            // Clean up trail sprites safely
            if (spark.trailSprites) {
                spark.trailSprites.forEach(sprite => {
                    try {
                        if (sprite && sprite.parent) sprite.parent.removeChild(sprite);
                        if (sprite) sprite.destroy();
                    } catch (e) {
                        // Ignore cleanup errors
                    }
                });
                spark.trailSprites = [];
            }
        }

        resetArc(arc) {
            if (!arc) return;

            arc.visible = true;
            arc._destroyed = false;
            arc.life = 0;
            arc.maxLife = 0;
            arc.opacity = 255;
        }

        resetSmoke(smoke) {
            if (!smoke) return;

            smoke.visible = true;
            smoke._destroyed = false;
            smoke.life = 0;
            smoke.maxLife = 0;
            smoke.opacity = 255;
            smoke.scale.set(1, 1);
        }

        update() {
            // Update spark generation with randomized breaks
            this.sparkTimer++;
            if (this.sparkTimer >= this.nextSparkDelay) {
                this.sparkTimer = 0;
                this.updateSparks();
                // Set next randomized delay
                this.nextSparkDelay = this.getRandomizedInterval(this.sparkInterval);
            }

            // Update arc generation with randomized breaks
            this.arcTimer++;
            if (this.arcTimer >= this.nextArcDelay) {
                this.arcTimer = 0;
                this.updateArcs();
                // Set next randomized delay
                this.nextArcDelay = this.getRandomizedInterval(this.arcInterval);
            }

            // Update smoke generation with randomized breaks
            this.smokeTimer++;
            if (this.smokeTimer >= this.nextSmokeDelay) {
                this.smokeTimer = 0;
                this.updateSmoke();
                // Set next randomized delay
                this.nextSmokeDelay = this.getRandomizedInterval(this.smokeInterval);
            }

            // Update spark layer position to match spriteset zoom (VisuStella compatibility)
            this.updateSparkLayerPosition();

            // Update existing spark sprites using object pooling
            this.activeSparkSprites = this.activeSparkSprites.filter(spark => {
                try {
                    if (!spark) return false;
                    const alive = spark.update();
                    if (!alive) {
                        if (this.sparkLayer && spark.parent) {
                            this.sparkLayer.removeChild(spark);
                        }
                        this.sparkPool.release(spark);
                    }
                    return alive;
                } catch (e) {
                    console.warn('EventSparkingEffect: Spark update error:', e);
                    return false;
                }
            });

            // Update existing arc sprites using object pooling
            this.activeArcSprites = this.activeArcSprites.filter(arc => {
                try {
                    if (!arc) return false;
                    const alive = arc.update();
                    if (!alive) {
                        if (this.sparkLayer && arc.parent) {
                            this.sparkLayer.removeChild(arc);
                        }
                        this.arcPool.release(arc);
                    }
                    return alive;
                } catch (e) {
                    console.warn('EventSparkingEffect: Arc update error:', e);
                    return false;
                }
            });

            // Update existing smoke sprites using object pooling
            this.activeSmokeSprites = this.activeSmokeSprites.filter(smoke => {
                try {
                    if (!smoke) return false;
                    const alive = smoke.update();
                    if (!alive) {
                        if (this.sparkLayer && smoke.parent) {
                            this.sparkLayer.removeChild(smoke);
                        }
                        this.smokePool.release(smoke);
                    }
                    return alive;
                } catch (e) {
                    console.warn('EventSparkingEffect: Smoke update error:', e);
                    return false;
                }
            });

            // Update screen flashes
            this.activeFlashes = this.activeFlashes.filter(flash => {
                try {
                    // Additional safety checks
                    if (!flash || flash._destroyed || !flash.parent) {
                        return false;
                    }
                    return flash.update();
                } catch (e) {
                    console.warn('EventSparkingEffect: Flash update error:', e);
                    // Mark as destroyed to prevent future errors
                    if (flash) {
                        flash._destroyed = true;
                    }
                    return false;
                }
            });
        }

        updateSparkLayerPosition() {
            // Match the spriteset's position and scale for VisuStella Camera Zoom compatibility
            const spriteset = this.scene._spriteset;
            if (!spriteset) return;

            // The spark layer should NOT inherit the spriteset's scale since we handle scaling per-spark
            // But it should follow the spriteset's position offset
            this.sparkLayer.x = 0; // Keep at origin since we calculate absolute positions
            this.sparkLayer.y = 0;
        }

        getRandomizedInterval(baseInterval) {
            // Create randomized breaks - sometimes much longer pauses
            const rand = Math.random();
            if (rand < 0.15) {
                // 15% chance of long break (2-4x longer)
                return baseInterval * (2 + Math.random() * 2);
            } else if (rand < 0.35) {
                // 20% chance of medium break (1.5-2x longer)
                return baseInterval * (1.5 + Math.random() * 0.5);
            } else {
                // 65% chance of normal timing with small variation
                return baseInterval * (0.7 + Math.random() * 0.6);
            }
        }

        updateSparks() {
            // Generate sparks for each sparking event
            this.sparkingEvents.forEach((data, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) return;

                // Get event sprite position (relative to spriteset)
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) return;

                // Use sprite's local position since spark layer is child of spriteset
                // This automatically inherits zoom scaling from the spriteset
                const screenX = sprite.x;
                const screenY = sprite.y - sprite.height * 0.3; // Above the event

                // Generate sparks (no manual zoom scaling needed)
                this.generateSparksAt(screenX, screenY, data.settings);
            });
        }

        updateArcs() {
            // Generate electrical arcs for each sparking event
            this.sparkingEvents.forEach((data, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) return;

                // Get event sprite position (relative to spriteset)
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) return;

                // Use sprite's local position since spark layer is child of spriteset
                const screenX = sprite.x;
                const screenY = sprite.y - sprite.height * 0.3; // Above the event

                // Generate electrical arcs
                this.generateArcsAt(screenX, screenY, data.settings);
            });
        }

        updateSmoke() {
            // Generate electrical smoke for each sparking event
            this.sparkingEvents.forEach((data, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) return;

                // Get event sprite position (relative to spriteset)
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) return;

                // Use sprite's local position since spark layer is child of spriteset
                const screenX = sprite.x;
                const screenY = sprite.y - sprite.height * 0.2; // Slightly above the event

                // Generate electrical smoke
                this.generateSmokeAt(screenX, screenY, data.settings);
            });
        }

        generateSparksAt(x, y, settings) {
            // Limit total sparks for performance
            if (this.activeSparkSprites.length >= 20) return; // Fewer total sparks

            // More intermittent behavior with longer breaks
            const burstChance = Math.random();
            let sparksToGenerate = 0;
            let isMajorBurst = false;

            if (burstChance < 0.5) {
                sparksToGenerate = 0; // No sparks 50% of the time (more breaks)
            } else if (burstChance < 0.8) {
                sparksToGenerate = 1; // Single spark 30% of the time
            } else if (burstChance < 0.95) {
                sparksToGenerate = 2; // Two sparks 15% of the time
            } else {
                sparksToGenerate = Math.min(settings.count, 3); // Burst 5% of the time
                isMajorBurst = true; // Trigger screen flash for major bursts
            }

            // Create localized flash for major electrical events (1/4 size)
            if (isMajorBurst && sparksToGenerate >= 2) {
                const flash = LocalizedFlash.create(this.scene, x, y, 0.6, 10, settings.color, 12);
                this.activeFlashes.push(flash);
            }

            for (let i = 0; i < sparksToGenerate; i++) {
                // Smaller, more concentrated spawn area
                const baseSpread = 16; // Smaller spread
                const offsetX = (Math.random() - 0.5) * baseSpread;
                const offsetY = (Math.random() - 0.5) * baseSpread;

                try {
                    // Get spark from pool and initialize
                    const spark = this.sparkPool.get();
                    if (!spark) continue;

                    spark.x = x + offsetX;
                    spark.y = y + offsetY;

                    // Reinitialize spark properties
                    const angle = Math.random() * Math.PI * 2;
                    const speed = settings.speed * (0.5 + Math.random() * 0.5);
                    spark.vx = Math.cos(angle) * speed;
                    spark.vy = Math.sin(angle) * speed - 1;
                    spark.life = settings.lifetime * (0.7 + Math.random() * 0.6);
                    spark.maxLife = spark.life;
                    spark.size = settings.size * (0.5 + Math.random() * 0.5);
                    spark.color = settings.color;
                    spark.sparkIntensity = 1.0;
                    spark.trail = [];
                    spark.trailSprites = [];

                    this.sparkLayer.addChild(spark);
                    this.activeSparkSprites.push(spark);
                } catch (e) {
                    console.warn('EventSparkingEffect: Spark creation error:', e);
                }
            }
        }

        generateArcsAt(x, y, settings) {
            // Limit total arcs for performance
            if (this.activeArcSprites.length >= 15) return; // Fewer total arcs

            // More intermittent arcs with longer quiet periods
            const arcChance = Math.random();
            let arcsToGenerate = 0;

            if (arcChance < 0.6) {
                arcsToGenerate = 0; // No arcs 60% of the time (more breaks)
            } else if (arcChance < 0.9) {
                arcsToGenerate = 1; // Single arc 30% of the time
            } else {
                arcsToGenerate = 2; // Two arcs 10% of the time
            }

            for (let i = 0; i < arcsToGenerate; i++) {
                // Very small spawn area for arcs - they're more localized
                const baseSpread = 12;
                const offsetX = (Math.random() - 0.5) * baseSpread;
                const offsetY = (Math.random() - 0.5) * baseSpread;

                // Get arc from pool and initialize
                const arc = this.arcPool.get();
                arc.x = x + offsetX;
                arc.y = y + offsetY;

                // Reinitialize arc properties
                arc.life = 8 + Math.random() * 12;
                arc.maxLife = arc.life;
                arc.length = 8 + Math.random() * 16;
                arc.angle = Math.random() * Math.PI * 2;
                arc.thickness = 0.5 + Math.random() * 1;
                arc.segments = 3 + Math.floor(Math.random() * 4);
                arc.color = settings.color;

                // Recreate arc bitmap with new properties
                arc.createArcBitmap();

                this.sparkLayer.addChild(arc);
                this.activeArcSprites.push(arc);
            }
        }

        generateSmokeAt(x, y, settings) {
            // Limit total smoke for performance
            if (this.activeSmokeSprites.length >= 5) return; // Very few smoke puffs

            // Smoke is occasional - 25% chance when called
            const smokeChance = Math.random();
            if (smokeChance > 0.25) return; // 75% chance of no smoke

            try {
                // Get smoke from pool and initialize
                const smoke = this.smokePool.get();
                if (!smoke) return;

                // Small random offset for smoke position
                const offsetX = (Math.random() - 0.5) * 8;
                const offsetY = (Math.random() - 0.5) * 8;

                smoke.x = x + offsetX;
                smoke.y = y + offsetY;

                // Reinitialize smoke properties
                smoke.vx = (Math.random() - 0.5) * 0.5;
                smoke.vy = -0.3 - Math.random() * 0.2;
                smoke.life = 120 + Math.random() * 60;
                smoke.maxLife = smoke.life;
                smoke.size = 4 + Math.random() * 8;
                smoke.color = settings.color;
                smoke.initialAlpha = 0.3 + Math.random() * 0.2;
                smoke.scale.set(1, 1);

                // Recreate smoke bitmap with new properties
                smoke.createSmokeBitmap();

                this.sparkLayer.addChild(smoke);
                this.activeSmokeSprites.push(smoke);
            } catch (e) {
                console.warn('EventSparkingEffect: Smoke creation error:', e);
            }
        }

        destroy() {
            // Clean up object pools
            this.sparkPool.clear();
            this.arcPool.clear();
            this.smokePool.clear();
            this.activeSparkSprites = [];
            this.activeArcSprites = [];
            this.activeSmokeSprites = [];
            
            // Clean up flashes properly
            this.cleanupAllFlashes();

            if (this.sparkLayer && this.sparkLayer.parent) {
                this.sparkLayer.parent.removeChild(this.sparkLayer);
            }
            this.sparkLayer.destroy();

            console.log('EventSparkingEffect: EventSparkManager destroyed with object pools cleaned');
        }
    }

    // Utility functions
    function parseSparkSettings(eventData) {
        if (!eventData) return null;

        // Check both the note field and comment event commands
        const note = eventData.note || '';
        const commentNotetags = getCommentNotetags(eventData);

        // Check for spark tags in either location
        const hasSparkTag = note.includes('<spark>') || note.includes('<spark:') ||
                           commentNotetags.some(comment => comment.includes('<spark>') || comment.includes('<spark:'));

        if (!hasSparkTag) {
            return null;
        }

        console.log('EventSparkingEffect: Spark tag found! Creating sparking effect');
        console.log('EventSparkingEffect: Note field contains spark tag:', note.includes('<spark>') || note.includes('<spark:'));
        console.log('EventSparkingEffect: Comment commands contain spark tag:', commentNotetags.some(comment => comment.includes('<spark>') || comment.includes('<spark:')));
        
        const settings = getDefaultSettings();

        // Parse custom parameters from note field first
        let match = note.match(/<spark:(.*?)>/);
        if (match) {
            parseSparkParameters(match[1], settings);
        }

        // Parse custom parameters from comment event commands (overrides note field)
        commentNotetags.forEach(comment => {
            const commentMatch = comment.match(/<spark:(.*?)>/);
            if (commentMatch) {
                parseSparkParameters(commentMatch[1], settings);
            }
        });

        return settings;
    }

    function getCommentNotetags(eventData) {
        const comments = [];
        
        // Check if the event has pages
        if (eventData.pages) {
            eventData.pages.forEach(page => {
                if (page.list) {
                    page.list.forEach(command => {
                        // Check for comment commands (code 108 for comments, 408 for comment continuation)
                        if (command.code === 108 || command.code === 408) {
                            if (command.parameters && command.parameters[0]) {
                                comments.push(command.parameters[0]);
                            }
                        }
                    });
                }
            });
        }
        
        // Debug logging for comment notetags
        if (comments.length > 0) {
            console.log('EventSparkingEffect: Found comment notetags:', comments);
        }
        
        return comments;
    }

    function parseSparkParameters(paramString, settings) {
        const params = paramString.split(',');
        params.forEach(param => {
            const [key, value] = param.split('=');
            if (key && value) {
                switch (key.trim()) {
                    case 'color':
                        settings.color = value.trim();
                        break;
                    case 'size':
                        settings.size = Number(value.trim());
                        break;
                    case 'count':
                        settings.count = Number(value.trim());
                        break;
                    case 'speed':
                        settings.speed = Number(value.trim());
                        break;
                    case 'lifetime':
                        settings.lifetime = Number(value.trim());
                        break;
                    case 'flicker':
                        settings.flicker = value.trim() === 'true';
                        break;
                }
            }
        });
    }

    function getDefaultSettings() {
        return {
            color: SPARK_COLOR,
            size: SPARK_SIZE, // Now 1.5 (much smaller)
            count: SPARK_COUNT, // Now 4 (fewer sparks)
            speed: SPARK_SPEED, // Now 3 (faster)
            lifetime: SPARK_LIFETIME, // Now 30 (shorter)
            flicker: SPARK_FLICKER
        };
    }

    //=============================================================================
    // Plugin Commands
    //=============================================================================

    if (typeof PluginManager !== 'undefined') {
        PluginManager.registerCommand(pluginName, "ToggleSparking", args => {
            const eventId = Number(args.eventId);
            const enabled = args.enabled === 'true';

            const event = $gameMap.event(eventId);
            if (event) {
                if (enabled) {
                    event.setMeta('spark', 'true');
                } else {
                    event.setMeta('spark', 'false');
                }
                $gameMap.requestRefresh();
            }
        });
    }

    //=============================================================================
    // Scene_Map Integration (Following FF6 Airship pattern)
    //=============================================================================

    const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
    Scene_Map.prototype.createSpriteset = function() {
        _Scene_Map_createSpriteset.call(this);

        // Create spark manager after spriteset is created (for VisuStella Camera Zoom compatibility)
        this._sparkManager = new EventSparkManager(this);

        console.log('EventSparkingEffect: Scene_Map spriteset created with spark manager');
    };

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);

        // Update spark manager
        if (this._sparkManager) {
            this._sparkManager.update();
        }
    };

    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        // Clean up spark manager
        if (this._sparkManager) {
            this._sparkManager.destroy();
            this._sparkManager = null;
        }

        _Scene_Map_terminate.call(this);
    };

    //=============================================================================
    // Game_Event Override
    //=============================================================================

    const _Game_Event_initialize = Game_Event.prototype.initialize;
    Game_Event.prototype.initialize = function(mapId, eventId) {
        _Game_Event_initialize.call(this, mapId, eventId);
        this._sparkEnabled = this.hasSparkNotetag();

        if (this._sparkEnabled) {
            console.log('EventSparkingEffect: Event initialized with spark enabled, ID:', eventId);

            // Register with spark manager when scene is ready
            setTimeout(() => {
                if (SceneManager._scene && SceneManager._scene._sparkManager) {
                    const settings = parseSparkSettings($dataMap.events[eventId]);
                    if (settings) {
                        SceneManager._scene._sparkManager.addSparkingEvent(eventId, settings);
                    }
                }
            }, 100);
        }
    };

    Game_Event.prototype.hasSparkNotetag = function() {
        const eventData = $dataMap.events[this.eventId()];
        if (!eventData) return false;
        
        // Check note field
        const note = eventData.note || '';
        if (note.includes('<spark>') || note.includes('<spark:')) {
            return true;
        }
        
        // Check comment event commands
        const commentNotetags = getCommentNotetags(eventData);
        return commentNotetags.some(comment => comment.includes('<spark>') || comment.includes('<spark:'));
    };

    //=============================================================================
    // Spriteset_Map Helper Methods
    //=============================================================================

    Spriteset_Map.prototype.findTargetSprite = function(target) {
        return this._characterSprites.find(sprite => sprite._character === target);
    };

    //=============================================================================
    // Utility Functions
    //=============================================================================

    // Add a method to check if an event should spark
    Game_Event.prototype.shouldSpark = function() {
        return this._sparkEnabled;
    };

    // Debug function to check all events for spark tags
    window.checkSparkEvents = function() {
        console.log('EventSparkingEffect: Checking all events for spark tags...');
        if (!$dataMap || !$dataMap.events) {
            console.log('EventSparkingEffect: No map data available');
            return;
        }

        $dataMap.events.forEach((eventData, index) => {
            if (eventData && parseSparkSettings(eventData)) {
                const note = eventData.note || '';
                const commentNotetags = getCommentNotetags(eventData);
                console.log(`EventSparkingEffect: Event ${index} has spark tag:`, {
                    note: note,
                    commentNotetags: commentNotetags
                });
            }
        });
    };

    // Debug function to manually force spark creation
    window.forceCreateSparks = function() {
        console.log('EventSparkingEffect: Manually forcing spark creation...');
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            // Find all events with spark tags and add them
            $dataMap.events.forEach((eventData, index) => {
                if (eventData && parseSparkSettings(eventData)) {
                    const settings = parseSparkSettings(eventData);
                    if (settings) {
                        SceneManager._scene._sparkManager.addSparkingEvent(index, settings);
                        console.log(`Added sparking event ${index} to manager`);
                    }
                }
            });
        }
    };

    // Debug function to test spark visibility
    window.testSparks = function() {
        console.log('EventSparkingEffect: Testing spark creation...');
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            console.log('Spark manager found:', SceneManager._scene._sparkManager);
            console.log('Active sparking events:', SceneManager._scene._sparkManager.sparkingEvents);
            console.log('Current spark sprites:', SceneManager._scene._sparkManager.activeSparkSprites.length);
        }
    };

    // Debug function to test comment event command notetags
    window.testCommentNotetags = function(eventId) {
        console.log('EventSparkingEffect: Testing comment notetags for event', eventId);
        if (!$dataMap || !$dataMap.events[eventId]) {
            console.log('Event not found');
            return;
        }
        
        const eventData = $dataMap.events[eventId];
        const commentNotetags = getCommentNotetags(eventData);
        const note = eventData.note || '';
        
        console.log('Event note field:', note);
        console.log('Comment notetags:', commentNotetags);
        console.log('Has spark tag:', parseSparkSettings(eventData) !== null);
        
        if (parseSparkSettings(eventData)) {
            const settings = parseSparkSettings(eventData);
            console.log('Final spark settings:', settings);
        }
    };

    // Debug function to test dynamic page switching
    window.testPageSwitch = function(eventId) {
        console.log('EventSparkingEffect: Testing page switch for event', eventId);
        if (!$dataMap || !$dataMap.events[eventId]) {
            console.log('Event not found');
            return;
        }
        
        const event = $gameMap.event(eventId);
        if (!event) {
            console.log('Event instance not found');
            return;
        }
        
        console.log('Current event page:', event.page());
        console.log('Current spark status:', event._sparkEnabled);
        console.log('Has spark notetag on current page:', event.hasSparkNotetag());
        
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            const isInManager = SceneManager._scene._sparkManager.sparkingEvents.has(eventId);
            console.log('Is in spark manager:', isInManager);
        }
    };

    // Debug function to force refresh an event
    window.forceEventRefresh = function(eventId) {
        console.log('EventSparkingEffect: Forcing refresh for event', eventId);
        const event = $gameMap.event(eventId);
        if (event) {
            event.refresh();
            console.log('Event refreshed');
        } else {
            console.log('Event not found');
        }
    };

    // Debug function to check flash states
    window.checkFlashStates = function() {
        console.log('EventSparkingEffect: Checking flash states...');
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            const manager = SceneManager._scene._sparkManager;
            console.log('Active flashes count:', manager.activeFlashes.length);
            manager.activeFlashes.forEach((flash, index) => {
                console.log(`Flash ${index}:`, {
                    destroyed: flash._destroyed,
                    hasParent: !!flash.parent,
                    hasScale: !!flash.scale,
                    duration: flash._flashDuration
                });
            });
        }
    };

    // Call debug function when plugin loads
    console.log('EventSparkingEffect: Plugin loaded successfully');
    
    // Enhanced Scene_Map integration
    const _Scene_Map_start = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function() {
        _Scene_Map_start.call(this);

        // Initialize sparking events after scene starts
        setTimeout(() => {
            if (typeof checkSparkEvents === 'function') {
                checkSparkEvents();
            }

            // Initialize all sparking events
            if (this._sparkManager) {
                this._sparkManager.refreshAllSparkingEvents();
            }
        }, 500);
    };

    // Also hook into map refresh to ensure sparks work after map changes
    const _Game_Map_refresh = Game_Map.prototype.refresh;
    Game_Map.prototype.refresh = function() {
        _Game_Map_refresh.call(this);

        // Refresh sparking events after map refresh
        setTimeout(() => {
            if (SceneManager._scene && SceneManager._scene._sparkManager) {
                SceneManager._scene._sparkManager.refreshAllSparkingEvents();
            }
        }, 100);
    };

    // Re-scan when an event's active page changes (this is the key for page switching!)
    const _Game_Event_setupPage = Game_Event.prototype.setupPage;
    Game_Event.prototype.setupPage = function() {
        _Game_Event_setupPage.call(this);
        
        // Check if sparking status has changed after page switch
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            const currentSparkStatus = this.hasSparkNotetag();
            const wasSparking = SceneManager._scene._sparkManager.sparkingEvents.has(this.eventId());
            
            if (currentSparkStatus && !wasSparking) {
                // Sparking was turned on - add to manager
                const settings = parseSparkSettings($dataMap.events[this.eventId()]);
                if (settings) {
                    SceneManager._scene._sparkManager.addSparkingEvent(this.eventId(), settings);
                    console.log(`EventSparkingEffect: Added sparking event ${this.eventId()} after page switch`);
                }
            } else if (!currentSparkStatus && wasSparking) {
                // Sparking was turned off - remove from manager
                SceneManager._scene._sparkManager.removeSparkingEvent(this.eventId());
                console.log(`EventSparkingEffect: Removed sparking event ${this.eventId()} after page switch`);
            }
        }
    };

})();